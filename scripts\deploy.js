const { TonClient, WalletContractV4, internal } = require('@ton/ton');
const { mnemonicToPrivateKey } = require('@ton/crypto');
const { Address, Cell, beginCell } = require('@ton/core');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * Развертывание смарт-контракта в сети TON
 */
async function deployContract() {
    console.log('🚀 Начинаем развертывание контракта...');
    
    try {
        // Подключение к сети TON
        const endpoint = process.env.TON_NETWORK === 'mainnet' 
            ? 'https://toncenter.com/api/v2/jsonRPC'
            : 'https://testnet.toncenter.com/api/v2/jsonRPC';
            
        const client = new TonClient({
            endpoint: endpoint,
            apiKey: process.env.TON_API_KEY
        });
        
        console.log(`🌐 Подключение к сети: ${process.env.TON_NETWORK}`);
        
        // Загружаем мнемонику кошелька (нужно создать отдельно)
        const mnemonicPath = path.join(__dirname, '../wallet.mnemonic');
        if (!fs.existsSync(mnemonicPath)) {
            console.error('❌ Файл wallet.mnemonic не найден!');
            console.log('📝 Создайте файл wallet.mnemonic с мнемонической фразой вашего кошелька');
            console.log('   Пример: word1 word2 word3 ... word24');
            process.exit(1);
        }
        
        const mnemonic = fs.readFileSync(mnemonicPath, 'utf8').trim().split(' ');
        const keyPair = await mnemonicToPrivateKey(mnemonic);
        
        // Создаем кошелек
        const workchain = 0;
        const wallet = WalletContractV4.create({ workchain, publicKey: keyPair.publicKey });
        const walletContract = client.open(wallet);
        
        console.log(`💼 Адрес кошелька: ${wallet.address.toString()}`);
        
        // Проверяем баланс
        const balance = await walletContract.getBalance();
        console.log(`💰 Баланс кошелька: ${balance / 1000000000} TON`);
        
        if (balance < 1000000000) { // Меньше 1 TON
            console.error('❌ Недостаточно средств для развертывания!');
            console.log('💡 Пополните кошелек минимум на 1 TON');
            process.exit(1);
        }
        
        // Загружаем скомпилированный код контракта
        const contractPath = path.join(__dirname, '../build/access_control.boc');
        if (!fs.existsSync(contractPath)) {
            console.error('❌ Скомпилированный контракт не найден!');
            console.log('🔨 Сначала выполните: npm run compile-contract');
            process.exit(1);
        }
        
        const contractCode = Cell.fromBoc(fs.readFileSync(contractPath))[0];
        
        // Создаем начальные данные контракта
        const initialData = beginCell()
            .storeAddress(wallet.address) // owner_address
            .storeDict(null) // user_balances (пустой словарь)
            .storeCoins(0) // service_balance
            .storeDict(null) // access_records (пустой словарь)
            .endCell();
        
        // Создаем StateInit
        const stateInit = {
            code: contractCode,
            data: initialData
        };
        
        // Вычисляем адрес контракта
        const contractAddress = new Address(workchain, stateInit.code.hash());
        console.log(`📍 Адрес контракта: ${contractAddress.toString()}`);
        
        // Проверяем, не развернут ли уже контракт
        try {
            const contractState = await client.getContractState(contractAddress);
            if (contractState.state === 'active') {
                console.log('⚠️  Контракт уже развернут по этому адресу!');
                return {
                    success: true,
                    contractAddress: contractAddress.toString(),
                    alreadyDeployed: true
                };
            }
        } catch (error) {
            // Контракт не существует, продолжаем развертывание
        }
        
        // Отправляем транзакцию развертывания
        const seqno = await walletContract.getSeqno();
        
        await walletContract.sendTransfer({
            secretKey: keyPair.secretKey,
            seqno: seqno,
            messages: [
                internal({
                    to: contractAddress,
                    value: '0.1', // 0.1 TON для инициализации
                    init: stateInit,
                    body: beginCell().endCell()
                })
            ]
        });
        
        console.log('📤 Транзакция развертывания отправлена...');
        
        // Ждем подтверждения
        let currentSeqno = seqno;
        while (currentSeqno === seqno) {
            console.log('⏳ Ожидание подтверждения...');
            await sleep(2000);
            currentSeqno = await walletContract.getSeqno();
        }
        
        console.log('✅ Контракт успешно развернут!');
        
        // Сохраняем адрес контракта в .env
        updateEnvFile('CONTRACT_ADDRESS', contractAddress.toString());
        updateEnvFile('OWNER_WALLET', wallet.address.toString());
        
        return {
            success: true,
            contractAddress: contractAddress.toString(),
            ownerWallet: wallet.address.toString(),
            transactionHash: 'pending'
        };
        
    } catch (error) {
        console.error('❌ Ошибка развертывания:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

// Вспомогательная функция для обновления .env файла
function updateEnvFile(key, value) {
    const envPath = path.join(__dirname, '../.env');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    const regex = new RegExp(`^${key}=.*$`, 'm');
    if (regex.test(envContent)) {
        envContent = envContent.replace(regex, `${key}=${value}`);
    } else {
        envContent += `\n${key}=${value}`;
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log(`📝 Обновлен .env: ${key}=${value}`);
}

// Вспомогательная функция задержки
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Запускаем развертывание если скрипт вызван напрямую
if (require.main === module) {
    deployContract().then(result => {
        if (result.success) {
            console.log('🎉 Развертывание завершено успешно!');
            console.log(`📍 Адрес контракта: ${result.contractAddress}`);
        } else {
            console.error('💥 Развертывание не удалось');
            process.exit(1);
        }
    });
}

module.exports = { deployContract };
