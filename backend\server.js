const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
require('dotenv').config();

const TonClient = require('./ton-client');
const Database = require('./database');
const DeviceAPI = require('./device-api');

class AccessControlServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 8080;
        this.tonClient = new TonClient();
        this.database = new Database();
        this.deviceAPI = new DeviceAPI();
        
        this.setupMiddleware();
        this.setupRoutes();
    }
    
    setupMiddleware() {
        // CORS для фронтенда
        this.app.use(cors({
            origin: process.env.FRONTEND_URL || 'http://localhost:3000',
            credentials: true
        }));
        
        this.app.use(bodyParser.json());
        this.app.use(bodyParser.urlencoded({ extended: true }));
        
        // Статические файлы фронтенда
        this.app.use(express.static(path.join(__dirname, '../frontend')));
        
        // Логирование запросов
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }
    
    setupRoutes() {
        // Главная страница
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '../frontend/index.html'));
        });
        
        // API маршруты
        this.app.get('/api/status', this.getStatus.bind(this));
        this.app.get('/api/contract-info', this.getContractInfo.bind(this));
        this.app.post('/api/check-payment', this.checkPayment.bind(this));
        this.app.post('/api/check-access', this.checkAccess.bind(this));
        this.app.post('/api/control-device', this.controlDevice.bind(this));
        this.app.get('/api/user-balance/:address', this.getUserBalance.bind(this));
        this.app.get('/api/service-balance', this.getServiceBalance.bind(this));
        this.app.post('/api/withdraw-user', this.withdrawUser.bind(this));
        this.app.post('/api/withdraw-service', this.withdrawService.bind(this));
        
        // Обработка ошибок
        this.app.use(this.errorHandler.bind(this));
    }
    
    // Статус сервера
    async getStatus(req, res) {
        try {
            const contractStatus = await this.tonClient.isContractDeployed();
            const dbStatus = await this.database.isConnected();
            
            res.json({
                success: true,
                server: 'running',
                contract: contractStatus ? 'deployed' : 'not deployed',
                database: dbStatus ? 'connected' : 'disconnected',
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Информация о контракте
    async getContractInfo(req, res) {
        try {
            const info = await this.tonClient.getContractInfo();
            res.json({
                success: true,
                data: info
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Проверка платежа
    async checkPayment(req, res) {
        try {
            const { transactionHash, userAddress } = req.body;
            
            if (!transactionHash || !userAddress) {
                return res.status(400).json({
                    success: false,
                    error: 'Требуются transactionHash и userAddress'
                });
            }
            
            const paymentResult = await this.tonClient.checkPayment(transactionHash, userAddress);
            
            if (paymentResult.success) {
                // Сохраняем информацию о платеже в БД
                await this.database.savePayment({
                    userAddress,
                    transactionHash,
                    amount: paymentResult.amount,
                    timestamp: new Date()
                });
            }
            
            res.json(paymentResult);
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Проверка доступа пользователя
    async checkAccess(req, res) {
        try {
            const { userAddress } = req.body;
            
            if (!userAddress) {
                return res.status(400).json({
                    success: false,
                    error: 'Требуется userAddress'
                });
            }
            
            const hasAccess = await this.tonClient.checkUserAccess(userAddress);
            
            res.json({
                success: true,
                hasAccess: hasAccess,
                userAddress: userAddress
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Управление устройством
    async controlDevice(req, res) {
        try {
            const { userAddress, action } = req.body;
            
            if (!userAddress || !action) {
                return res.status(400).json({
                    success: false,
                    error: 'Требуются userAddress и action'
                });
            }
            
            // Проверяем доступ пользователя
            const hasAccess = await this.tonClient.checkUserAccess(userAddress);
            
            if (!hasAccess) {
                return res.status(403).json({
                    success: false,
                    error: 'Доступ запрещен. Необходимо оплатить доступ.'
                });
            }
            
            // Отправляем команду на устройство
            const deviceResult = await this.deviceAPI.sendCommand(action, userAddress);
            
            // Логируем действие
            await this.database.logDeviceAction({
                userAddress,
                action,
                timestamp: new Date(),
                success: deviceResult.success
            });
            
            res.json({
                success: true,
                message: 'Команда отправлена на устройство',
                deviceResponse: deviceResult
            });
            
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Получить баланс пользователя
    async getUserBalance(req, res) {
        try {
            const { address } = req.params;
            const balance = await this.tonClient.getUserBalance(address);
            
            res.json({
                success: true,
                balance: balance,
                address: address
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Получить баланс сервиса
    async getServiceBalance(req, res) {
        try {
            const balance = await this.tonClient.getServiceBalance();
            
            res.json({
                success: true,
                balance: balance
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Вывод средств пользователем
    async withdrawUser(req, res) {
        try {
            const { userAddress } = req.body;
            
            const result = await this.tonClient.withdrawUser(userAddress);
            
            res.json(result);
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Вывод средств сервиса
    async withdrawService(req, res) {
        try {
            const result = await this.tonClient.withdrawService();
            
            res.json(result);
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
    
    // Обработчик ошибок
    errorHandler(error, req, res, next) {
        console.error('Ошибка сервера:', error);
        
        res.status(500).json({
            success: false,
            error: 'Внутренняя ошибка сервера',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
    
    // Запуск сервера
    async start() {
        try {
            // Инициализация компонентов
            await this.tonClient.initialize();
            await this.database.initialize();
            await this.deviceAPI.initialize();
            
            this.app.listen(this.port, () => {
                console.log(`🚀 Сервер запущен на порту ${this.port}`);
                console.log(`🌐 Фронтенд доступен по адресу: http://localhost:${this.port}`);
                console.log(`📡 API доступен по адресу: http://localhost:${this.port}/api`);
            });
            
        } catch (error) {
            console.error('❌ Ошибка запуска сервера:', error);
            process.exit(1);
        }
    }
}

// Запуск сервера
if (require.main === module) {
    const server = new AccessControlServer();
    server.start();
}

module.exports = AccessControlServer;
