;; Standard library for TON smart contracts

;; Math operations
int min(int a, int b) asm "MIN";
int max(int a, int b) asm "MAX";

;; Cell operations
builder store_coins(builder b, int x) asm "STGRAMS";
(slice, int) load_coins(slice s) asm( -> 1 0) "LDGRAMS";

;; Dictionary operations
(cell, int) udict_get?(cell dict, int key_len, int index) asm(index dict key_len) "DICTUGET" "NULLSWAPIFNOT";
(cell, int) udict_set?(cell dict, int key_len, int index, slice value) asm(value index dict key_len) "DICTUSET";
(cell, int) udict_delete?(cell dict, int key_len, int index) asm(index dict key_len) "DICTUDEL";
cell udict_set_builder(cell dict, int key_len, int index, builder value) asm(value index dict key_len) "DICTUSETB";

;; Address operations
int slice_hash(slice s) asm "HASHSU";
int equal_slices(slice a, slice b) asm "SDEQ";

;; Message operations
() send_raw_message(cell msg, int mode) impure asm "SENDRAWMSG";

;; Time operations
int now() asm "NOW";

;; Storage operations
cell get_data() asm "c4 PUSH";
() set_data(cell c) impure asm "c4 POP";

;; Exception handling
() throw(int excno) impure asm "THROW";
() throw_if(int excno, int cond) impure asm "THROWIF";
() throw_unless(int excno, int cond) impure asm "THROWIFNOT";

;; Slice operations
int slice_empty?(slice s) asm "SEMPTY";
slice begin_parse(cell c) asm "CTOS";
builder begin_cell() asm "NEWC";
cell end_cell(builder b) asm "ENDC";

;; Builder operations
builder store_uint(builder b, int x, int len) asm(x b len) "STUX";
builder store_int(builder b, int x, int len) asm(x b len) "STIX";
builder store_slice(builder b, slice s) asm "STSLICER";
builder store_dict(builder b, cell c) asm(c b) "STDICT";

;; Slice loading operations
(slice, int) load_uint(slice s, int len) asm(s len -> 1 0) "LDUX";
(slice, int) load_int(slice s, int len) asm(s len -> 1 0) "LDIX";
(slice, slice) load_msg_addr(slice s) asm( -> 1 0) "LDMSGADDR";
(slice, cell) load_dict(slice s) asm( -> 1 0) "LDDICT";

;; Prefix operations
slice skip_bits(slice s, int len) asm "SDSKIPFIRST";
(slice, ()) ~skip_bits(slice s, int len) asm "SDSKIPFIRST";

;; Loading with modification
(slice, int) ~load_uint(slice s, int len) asm(s len -> 1 0) "LDUX";
(slice, int) ~load_int(slice s, int len) asm(s len -> 1 0) "LDIX";
(slice, slice) ~load_msg_addr(slice s) asm( -> 1 0) "LDMSGADDR";
(slice, cell) ~load_dict(slice s) asm( -> 1 0) "LDDICT";
(slice, int) ~load_coins(slice s) asm( -> 1 0) "LDGRAMS";
