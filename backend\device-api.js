const axios = require('axios');

class DeviceAPI {
    constructor() {
        this.deviceUrl = process.env.DEVICE_API_URL || 'http://localhost:9000';
        this.apiKey = process.env.DEVICE_API_KEY || 'default_key';
        this.isSimulated = true; // Для демонстрации используем симуляцию
    }
    
    async initialize() {
        console.log('🔌 Инициализация Device API...');
        
        try {
            // Проверяем доступность устройства
            await this.checkConnection();
            console.log('✅ Device API инициализирован');
        } catch (error) {
            console.log('⚠️  Устройство недоступно, используется симуляция');
            this.isSimulated = true;
        }
    }
    
    async checkConnection() {
        if (this.isSimulated) {
            return { status: 'simulated', available: true };
        }
        
        try {
            const response = await axios.get(`${this.deviceUrl}/status`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });
            
            return {
                status: 'connected',
                available: true,
                deviceInfo: response.data
            };
        } catch (error) {
            throw new Error(`Устройство недоступно: ${error.message}`);
        }
    }
    
    async sendCommand(action, userAddress) {
        console.log(`🎮 Отправка команды: ${action} от пользователя ${userAddress}`);
        
        if (this.isSimulated) {
            return this.simulateCommand(action, userAddress);
        }
        
        try {
            const response = await axios.post(`${this.deviceUrl}/control`, {
                action: action,
                userAddress: userAddress,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            
            return {
                success: true,
                action: action,
                response: response.data,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('❌ Ошибка отправки команды на устройство:', error.message);
            
            return {
                success: false,
                action: action,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    
    // Симуляция работы с устройством для демонстрации
    async simulateCommand(action, userAddress) {
        // Имитируем задержку сети
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
        
        const responses = {
            'open_door': {
                success: true,
                message: 'Дверь открыта',
                duration: 5000, // 5 секунд
                deviceId: 'door_001'
            },
            'turn_on_light': {
                success: true,
                message: 'Свет включен',
                brightness: 100,
                deviceId: 'light_001'
            },
            'start_motor': {
                success: true,
                message: 'Мотор запущен',
                speed: 1500,
                deviceId: 'motor_001'
            },
            'activate_pump': {
                success: true,
                message: 'Насос активирован',
                pressure: 2.5,
                deviceId: 'pump_001'
            },
            'unlock_gate': {
                success: true,
                message: 'Ворота разблокированы',
                duration: 10000, // 10 секунд
                deviceId: 'gate_001'
            }
        };
        
        // Случайная вероятность ошибки (5%)
        if (Math.random() < 0.05) {
            return {
                success: false,
                action: action,
                error: 'Устройство временно недоступно',
                timestamp: new Date().toISOString()
            };
        }
        
        const response = responses[action] || {
            success: true,
            message: `Команда "${action}" выполнена`,
            deviceId: 'unknown_device'
        };
        
        console.log(`✅ Команда выполнена: ${response.message}`);
        
        return {
            success: response.success,
            action: action,
            response: response,
            timestamp: new Date().toISOString(),
            simulated: true
        };
    }
    
    // Получение статуса устройства
    async getDeviceStatus(deviceId = null) {
        if (this.isSimulated) {
            return this.simulateDeviceStatus(deviceId);
        }
        
        try {
            const url = deviceId 
                ? `${this.deviceUrl}/device/${deviceId}/status`
                : `${this.deviceUrl}/status`;
                
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });
            
            return {
                success: true,
                status: response.data
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Симуляция статуса устройства
    async simulateDeviceStatus(deviceId) {
        const devices = {
            'door_001': {
                id: 'door_001',
                name: 'Главная дверь',
                type: 'door',
                status: 'closed',
                lastAction: new Date(Date.now() - Math.random() * 3600000).toISOString(),
                online: true
            },
            'light_001': {
                id: 'light_001',
                name: 'Основное освещение',
                type: 'light',
                status: 'off',
                brightness: 0,
                lastAction: new Date(Date.now() - Math.random() * 3600000).toISOString(),
                online: true
            },
            'motor_001': {
                id: 'motor_001',
                name: 'Главный мотор',
                type: 'motor',
                status: 'stopped',
                speed: 0,
                lastAction: new Date(Date.now() - Math.random() * 3600000).toISOString(),
                online: true
            }
        };
        
        if (deviceId) {
            return {
                success: true,
                status: devices[deviceId] || { error: 'Устройство не найдено' }
            };
        }
        
        return {
            success: true,
            status: {
                totalDevices: Object.keys(devices).length,
                onlineDevices: Object.values(devices).filter(d => d.online).length,
                devices: Object.values(devices),
                lastUpdate: new Date().toISOString()
            }
        };
    }
    
    // Получение доступных команд
    getAvailableCommands() {
        return [
            {
                action: 'open_door',
                name: 'Открыть дверь',
                description: 'Открывает главную дверь на 5 секунд',
                icon: '🚪'
            },
            {
                action: 'turn_on_light',
                name: 'Включить свет',
                description: 'Включает основное освещение',
                icon: '💡'
            },
            {
                action: 'start_motor',
                name: 'Запустить мотор',
                description: 'Запускает главный мотор',
                icon: '⚙️'
            },
            {
                action: 'activate_pump',
                name: 'Активировать насос',
                description: 'Включает водяной насос',
                icon: '🚰'
            },
            {
                action: 'unlock_gate',
                name: 'Разблокировать ворота',
                description: 'Разблокирует ворота на 10 секунд',
                icon: '🚧'
            }
        ];
    }
}

module.exports = DeviceAPI;
