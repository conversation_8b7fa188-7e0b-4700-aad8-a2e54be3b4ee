/**
 * Copyright (c) Whales Corp. 
 * All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

import { from<PERSON><PERSON>, to<PERSON><PERSON> } from "./convert";

const stringCases: { nano: string, real: string }[] = [
    { real: '1', nano: '**********' },
    { real: '10', nano: '**********0' },
    { real: '0.1', nano: '100000000' },
    { real: '0.33', nano: '330000000' },
    { real: '0.000000001', nano: '1' },
    { real: '10.000000001', nano: '**********1' },
    { real: '1000000.000000001', nano: '**********000001' },
    { real: '**********00', nano: '**********00000000000' },
];

const numberCases: { nano: string, real: number }[] = [
    { real: -0, nano: '0' },
    { real: 0, nano: '0' },
    { real: 1e64, nano: '**********0000000000000000000000000000000000000000000000000000000000000000' },
    { real: 1, nano: '**********' },
    { real: 10, nano: '**********0' },
    { real: 0.1, nano: '100000000' },
    { real: 0.33, nano: '330000000' },
    { real: 0.000000001, nano: '1' },
    { real: 10.000000001, nano: '**********1' },
    { real: 1000000.000000001, nano: '**********000001' },
    { real: **********00, nano: '**********00000000000' },
];

describe('convert', () => {
    it('should throw an error for NaN', () => {
        expect(() => toNano(NaN)).toThrow();
    });
    it('should throw an error for Infinity', () => {
        expect(() => toNano(Infinity)).toThrow();
    });
    it('should throw an error for -Infinity', () => {
        expect(() => toNano(-Infinity)).toThrow();
    });
    it('should throw an error due to insufficient precision of number', () => {
        expect(() => toNano(10000000.000000001)).toThrow();
    });
    it('should convert numbers toNano', () => {
        for (let r of numberCases) {
            let c = toNano(r.real);
            expect(c).toBe(BigInt(r.nano));
        }
    });
    it('should convert strings toNano', () => {
        for (let r of stringCases) {
            let c = toNano(r.real);
            expect(c).toBe(BigInt(r.nano));
        }
    });
    it('should convert fromNano', () => {
        for (let r of stringCases) {
            let c = fromNano(r.nano);
            expect(c).toEqual(r.real);
        }
    });
});