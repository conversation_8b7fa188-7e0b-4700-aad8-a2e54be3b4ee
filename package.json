{"name": "ton-access-control-system", "version": "1.0.0", "description": "Накопительный смарт-контракт на TON для управления доступом к устройствам", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "compile-contract": "node scripts/compile.js", "deploy-contract": "node scripts/deploy.js", "test": "jest", "build-frontend": "npm run build:css", "build:css": "echo 'CSS build complete'", "serve": "http-server frontend -p 3000"}, "keywords": ["TON", "blockchain", "smart-contract", "access-control", "payment"], "author": "TON Developer", "license": "MIT", "dependencies": {"@ton/core": "^0.56.0", "@ton/crypto": "^3.2.0", "@ton/ton": "^13.11.0", "@tonconnect/sdk": "^3.0.0", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "axios": "^1.6.0", "body-parser": "^1.20.2", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "http-server": "^14.1.1", "@types/node": "^20.8.0"}}