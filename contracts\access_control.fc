#include "stdlib.fc";

;; Константы
const int ACCESS_PRICE = 1000000000; ;; 1 TON в nanotons
const int SERVICE_FEE_PERCENT = 10;   ;; 10% комиссия
const int ACCESS_DURATION = 3600;     ;; 1 час доступа в секундах

;; Коды операций
const int OP_PAY_ACCESS = 1;
const int OP_WITHDRAW_USER = 2;
const int OP_WITHDRAW_SERVICE = 3;
const int OP_CHECK_ACCESS = 4;

;; Структура данных контракта
;; storage: owner_address user_balances service_balance access_records

;; Загрузка данных из хранилища
(slice, cell, int, cell) load_data() inline {
    slice ds = get_data().begin_parse();
    slice owner_address = ds~load_msg_addr();
    cell user_balances = ds~load_dict();
    int service_balance = ds~load_coins();
    cell access_records = ds~load_dict();
    return (owner_address, user_balances, service_balance, access_records);
}

;; Сохранение данных в хранилище
() save_data(slice owner_address, cell user_balances, int service_balance, cell access_records) impure inline {
    set_data(begin_cell()
        .store_slice(owner_address)
        .store_dict(user_balances)
        .store_coins(service_balance)
        .store_dict(access_records)
        .end_cell());
}

;; Получение баланса пользователя
int get_user_balance(cell user_balances, slice user_address) inline {
    int user_hash = slice_hash(user_address);
    (slice balance_slice, int found) = user_balances.udict_get?(256, user_hash);
    if (found) {
        return balance_slice~load_coins();
    }
    return 0;
}

;; Обновление баланса пользователя
cell set_user_balance(cell user_balances, slice user_address, int new_balance) inline {
    int user_hash = slice_hash(user_address);
    if (new_balance > 0) {
        return user_balances.udict_set_builder(256, user_hash, 
            begin_cell().store_coins(new_balance));
    } else {
        (user_balances, int success) = user_balances.udict_delete?(256, user_hash);
        return user_balances;
    }
}

;; Проверка доступа пользователя
int check_user_access(cell access_records, slice user_address) inline {
    int user_hash = slice_hash(user_address);
    (slice access_slice, int found) = access_records.udict_get?(256, user_hash);
    if (found) {
        int access_until = access_slice~load_uint(32);
        return access_until > now();
    }
    return 0;
}

;; Установка времени доступа
cell set_user_access(cell access_records, slice user_address, int access_until) inline {
    int user_hash = slice_hash(user_address);
    return access_records.udict_set_builder(256, user_hash,
        begin_cell().store_uint(access_until, 32));
}

;; Основная функция обработки сообщений
() recv_internal(int my_balance, int msg_value, cell in_msg_full, slice in_msg_body) impure {
    if (in_msg_body.slice_empty?()) {
        return (); ;; Простой перевод без операции
    }

    slice cs = in_msg_full.begin_parse();
    int flags = cs~load_uint(4);
    slice sender_address = cs~load_msg_addr();
    
    int op = in_msg_body~load_uint(32);
    
    (slice owner_address, cell user_balances, int service_balance, cell access_records) = load_data();
    
    if (op == OP_PAY_ACCESS) {
        ;; Оплата доступа
        throw_unless(100, msg_value >= ACCESS_PRICE);
        
        ;; Расчет комиссии сервиса
        int service_fee = (msg_value * SERVICE_FEE_PERCENT) / 100;
        int user_amount = msg_value - service_fee;
        
        ;; Обновление балансов
        int current_balance = get_user_balance(user_balances, sender_address);
        user_balances = set_user_balance(user_balances, sender_address, current_balance + user_amount);
        service_balance += service_fee;
        
        ;; Предоставление доступа
        int access_until = now() + ACCESS_DURATION;
        access_records = set_user_access(access_records, sender_address, access_until);
        
        save_data(owner_address, user_balances, service_balance, access_records);
        return ();
    }
    
    if (op == OP_WITHDRAW_USER) {
        ;; Вывод средств пользователем
        int user_balance = get_user_balance(user_balances, sender_address);
        throw_unless(101, user_balance > 0);
        
        ;; Обнуление баланса пользователя
        user_balances = set_user_balance(user_balances, sender_address, 0);
        
        ;; Отправка средств пользователю
        var msg = begin_cell()
            .store_uint(0x18, 6)
            .store_slice(sender_address)
            .store_coins(user_balance)
            .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
            .end_cell();
        send_raw_message(msg, 1);
        
        save_data(owner_address, user_balances, service_balance, access_records);
        return ();
    }
    
    if (op == OP_WITHDRAW_SERVICE) {
        ;; Вывод средств сервиса (только владелец)
        throw_unless(102, equal_slices(sender_address, owner_address));
        throw_unless(103, service_balance > 0);
        
        ;; Отправка средств владельцу
        var msg = begin_cell()
            .store_uint(0x18, 6)
            .store_slice(owner_address)
            .store_coins(service_balance)
            .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
            .end_cell();
        send_raw_message(msg, 1);
        
        service_balance = 0;
        save_data(owner_address, user_balances, service_balance, access_records);
        return ();
    }
    
    throw(0xffff); ;; Неизвестная операция
}

;; Get-методы для чтения данных

;; Получить баланс пользователя
int get_user_balance_method(slice user_address) method_id {
    (slice owner_address, cell user_balances, int service_balance, cell access_records) = load_data();
    return get_user_balance(user_balances, user_address);
}

;; Проверить доступ пользователя
int get_user_access(slice user_address) method_id {
    (slice owner_address, cell user_balances, int service_balance, cell access_records) = load_data();
    return check_user_access(access_records, user_address);
}

;; Получить баланс сервиса
int get_service_balance() method_id {
    (slice owner_address, cell user_balances, int service_balance, cell access_records) = load_data();
    return service_balance;
}

;; Получить адрес владельца
slice get_owner_address() method_id {
    (slice owner_address, cell user_balances, int service_balance, cell access_records) = load_data();
    return owner_address;
}
