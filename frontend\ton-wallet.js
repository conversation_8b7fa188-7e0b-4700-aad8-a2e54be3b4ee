/**
 * TON Wallet Integration
 * Интеграция с кошельками TON через TonConnect
 */

class TonWallet {
    constructor() {
        this.tonConnectUI = null;
        this.wallet = null;
        this.isConnected = false;
        this.userAddress = null;
        
        this.init();
    }
    
    async init() {
        try {
            // Инициализация TonConnect UI
            this.tonConnectUI = new TON_CONNECT_UI.TonConnectUI({
                manifestUrl: window.location.origin + '/tonconnect-manifest.json',
                buttonRootId: 'connectWallet'
            });
            
            // Слушаем изменения состояния подключения
            this.tonConnectUI.onStatusChange(wallet => {
                this.handleWalletChange(wallet);
            });
            
            // Проверяем, есть ли уже подключенный кошелек
            const currentWallet = this.tonConnectUI.wallet;
            if (currentWallet) {
                this.handleWalletChange(currentWallet);
            }
            
            console.log('✅ TonConnect инициализирован');
            
        } catch (error) {
            console.error('❌ Ошибка инициализации TonConnect:', error);
            this.showNotification('Ошибка инициализации кошелька', 'error');
        }
    }
    
    handleWalletChange(wallet) {
        if (wallet) {
            this.wallet = wallet;
            this.isConnected = true;
            this.userAddress = wallet.account.address;
            
            console.log('✅ Кошелек подключен:', this.userAddress);
            this.updateWalletUI();
            this.showNotification('Кошелек успешно подключен!', 'success');
            
            // Уведомляем приложение о подключении
            if (window.app) {
                window.app.onWalletConnected(this.userAddress);
            }
        } else {
            this.wallet = null;
            this.isConnected = false;
            this.userAddress = null;
            
            console.log('❌ Кошелек отключен');
            this.updateWalletUI();
            this.showNotification('Кошелек отключен', 'warning');
            
            // Уведомляем приложение об отключении
            if (window.app) {
                window.app.onWalletDisconnected();
            }
        }
    }
    
    updateWalletUI() {
        const walletInfo = document.getElementById('walletInfo');
        const walletStatus = document.getElementById('walletStatus');
        
        if (this.isConnected) {
            const shortAddress = this.formatAddress(this.userAddress);
            
            walletInfo.innerHTML = `
                <div class="wallet-address">${shortAddress}</div>
                <button id="disconnectWallet" class="btn btn-danger">
                    Отключить
                </button>
            `;
            
            walletStatus.textContent = 'Подключен';
            walletStatus.style.color = '#10b981';
            
            // Добавляем обработчик для кнопки отключения
            document.getElementById('disconnectWallet').addEventListener('click', () => {
                this.disconnect();
            });
            
        } else {
            walletInfo.innerHTML = `
                <button id="connectWallet" class="btn btn-primary">
                    Подключить кошелек
                </button>
            `;
            
            walletStatus.textContent = 'Не подключен';
            walletStatus.style.color = '#ef4444';
        }
    }
    
    async connect() {
        try {
            if (this.tonConnectUI) {
                await this.tonConnectUI.openModal();
            }
        } catch (error) {
            console.error('❌ Ошибка подключения кошелька:', error);
            this.showNotification('Ошибка подключения кошелька', 'error');
        }
    }
    
    async disconnect() {
        try {
            if (this.tonConnectUI) {
                await this.tonConnectUI.disconnect();
            }
        } catch (error) {
            console.error('❌ Ошибка отключения кошелька:', error);
            this.showNotification('Ошибка отключения кошелька', 'error');
        }
    }
    
    async sendTransaction(transaction) {
        if (!this.isConnected) {
            throw new Error('Кошелек не подключен');
        }
        
        try {
            console.log('📤 Отправка транзакции:', transaction);
            
            const result = await this.tonConnectUI.sendTransaction(transaction);
            
            console.log('✅ Транзакция отправлена:', result);
            return result;
            
        } catch (error) {
            console.error('❌ Ошибка отправки транзакции:', error);
            throw error;
        }
    }
    
    async sendPayment(toAddress, amount, payload = null) {
        const transaction = {
            validUntil: Math.floor(Date.now() / 1000) + 300, // 5 минут
            messages: [
                {
                    address: toAddress,
                    amount: amount.toString(),
                    payload: payload
                }
            ]
        };
        
        return await this.sendTransaction(transaction);
    }
    
    async payForAccess(contractAddress, accessPrice) {
        try {
            // Создаем payload для оплаты доступа (op = 1)
            const payload = this.createPaymentPayload(1); // OP_PAY_ACCESS = 1
            
            const result = await this.sendPayment(contractAddress, accessPrice, payload);
            
            this.showNotification('Платеж отправлен! Ожидание подтверждения...', 'success');
            
            return result;
            
        } catch (error) {
            console.error('❌ Ошибка оплаты доступа:', error);
            this.showNotification('Ошибка оплаты: ' + error.message, 'error');
            throw error;
        }
    }
    
    async withdrawUserFunds(contractAddress) {
        try {
            // Создаем payload для вывода средств пользователя (op = 2)
            const payload = this.createPaymentPayload(2); // OP_WITHDRAW_USER = 2
            
            const result = await this.sendPayment(contractAddress, '50000000', payload); // 0.05 TON для газа
            
            this.showNotification('Запрос на вывод отправлен!', 'success');
            
            return result;
            
        } catch (error) {
            console.error('❌ Ошибка вывода средств:', error);
            this.showNotification('Ошибка вывода: ' + error.message, 'error');
            throw error;
        }
    }
    
    createPaymentPayload(operation) {
        // Создаем простой payload с кодом операции
        // В реальном приложении здесь должна быть правильная сериализация
        const payload = new Uint8Array(4);
        const view = new DataView(payload.buffer);
        view.setUint32(0, operation, false); // big-endian
        
        return btoa(String.fromCharCode(...payload));
    }
    
    formatAddress(address) {
        if (!address) return '';
        
        // Форматируем адрес для отображения
        if (address.length > 10) {
            return `${address.slice(0, 6)}...${address.slice(-4)}`;
        }
        return address;
    }
    
    showNotification(message, type = 'info') {
        // Создаем уведомление
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Добавляем в контейнер уведомлений
        const container = document.getElementById('notifications');
        container.appendChild(notification);
        
        // Автоматически удаляем через 5 секунд
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    // Геттеры для удобства
    get address() {
        return this.userAddress;
    }
    
    get connected() {
        return this.isConnected;
    }
}

// Создаем глобальный экземпляр
window.tonWallet = new TonWallet();
