/**
 * Основное приложение TON Access Control
 */

class AccessControlApp {
    constructor() {
        this.apiUrl = window.location.origin + '/api';
        this.contractAddress = null;
        this.userAddress = null;
        this.hasAccess = false;
        this.userBalance = '0';
        this.accessPrice = '1000000000'; // 1 TON в nanotons

        this.currentSection = 'control';
        this.updateInterval = null;

        this.init();
    }

    async init() {
        console.log('🚀 Инициализация приложения...');

        try {
            // Загружаем информацию о контракте
            await this.loadContractInfo();

            // Настраиваем обработчики событий
            this.setupEventListeners();

            // Загружаем доступные команды устройства
            await this.loadDeviceCommands();

            console.log('✅ Приложение инициализировано');

        } catch (error) {
            console.error('❌ Ошибка инициализации приложения:', error);
            this.showNotification('Ошибка инициализации приложения', 'error');
        }
    }

    async loadContractInfo() {
        try {
            const response = await fetch(`${this.apiUrl}/contract-info`);
            const data = await response.json();

            if (data.success) {
                this.contractAddress = data.data.address;
                console.log('📍 Адрес контракта:', this.contractAddress);
            } else {
                throw new Error(data.error || 'Не удалось загрузить информацию о контракте');
            }
        } catch (error) {
            console.error('❌ Ошибка загрузки информации о контракте:', error);
            throw error;
        }
    }

    setupEventListeners() {
        // Кнопка оплаты доступа
        const payButton = document.getElementById('payForAccess');
        if (payButton) {
            payButton.addEventListener('click', () => this.payForAccess());
        }

        // Кнопка вывода средств
        const withdrawButton = document.getElementById('withdrawBalance');
        if (withdrawButton) {
            withdrawButton.addEventListener('click', () => this.withdrawUserFunds());
        }

        // Навигация
        const navButtons = document.querySelectorAll('.nav-btn');
        navButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.target.dataset.section;
                this.showSection(section);
            });
        });

        // Табы в истории
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                this.showHistoryTab(tab);
            });
        });

        // Закрытие модального окна
        const closeModal = document.getElementById('closeModal');
        if (closeModal) {
            closeModal.addEventListener('click', () => this.hideModal());
        }
    }

    async loadDeviceCommands() {
        try {
            // В реальном приложении это должно загружаться с сервера
            const commands = [
                {
                    action: 'open_door',
                    name: 'Открыть дверь',
                    description: 'Открывает главную дверь на 5 секунд',
                    icon: '🚪'
                },
                {
                    action: 'turn_on_light',
                    name: 'Включить свет',
                    description: 'Включает основное освещение',
                    icon: '💡'
                },
                {
                    action: 'start_motor',
                    name: 'Запустить мотор',
                    description: 'Запускает главный мотор',
                    icon: '⚙️'
                },
                {
                    action: 'activate_pump',
                    name: 'Активировать насос',
                    description: 'Включает водяной насос',
                    icon: '🚰'
                },
                {
                    action: 'unlock_gate',
                    name: 'Разблокировать ворота',
                    description: 'Разблокирует ворота на 10 секунд',
                    icon: '🚧'
                }
            ];

            this.renderDeviceCommands(commands);

        } catch (error) {
            console.error('❌ Ошибка загрузки команд устройства:', error);
        }
    }

    renderDeviceCommands(commands) {
        const container = document.getElementById('controlButtons');
        if (!container) return;

        container.innerHTML = commands.map(cmd => `
            <div class="control-btn" data-action="${cmd.action}">
                <span class="icon">${cmd.icon}</span>
                <div class="name">${cmd.name}</div>
                <div class="description">${cmd.description}</div>
            </div>
        `).join('');

        // Добавляем обработчики событий
        container.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.executeDeviceCommand(action);
            });
        });
    }

    // Обработчики событий кошелька
    onWalletConnected(address) {
        this.userAddress = address;
        console.log('👤 Пользователь подключен:', address);

        // Показываем секцию оплаты
        this.showPaymentSection();

        // Запускаем периодическое обновление данных
        this.startDataUpdates();

        // Проверяем доступ пользователя
        this.checkUserAccess();
    }

    onWalletDisconnected() {
        this.userAddress = null;
        this.hasAccess = false;
        this.userBalance = '0';

        console.log('👤 Пользователь отключен');

        // Останавливаем обновления
        this.stopDataUpdates();

        // Скрываем все секции
        this.hideAllSections();
    }

    showPaymentSection() {
        const paymentSection = document.getElementById('paymentSection');
        if (paymentSection) {
            paymentSection.style.display = 'block';
        }
    }

    hideAllSections() {
        const sections = ['paymentSection', 'controlSection', 'dashboardSection', 'navigation'];
        sections.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });
    }

    showSection(sectionName) {
        // Скрываем все секции
        const sections = ['controlSection', 'dashboardSection'];
        sections.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });

        // Показываем выбранную секцию
        const targetSection = document.getElementById(sectionName + 'Section');
        if (targetSection) {
            targetSection.style.display = 'block';
        }

        // Обновляем активную кнопку навигации
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.section === sectionName) {
                btn.classList.add('active');
            }
        });

        this.currentSection = sectionName;

        // Загружаем данные для секции
        if (sectionName === 'dashboard') {
            this.loadDashboardData();
        }
    }

    startDataUpdates() {
        // Обновляем данные каждые 10 секунд
        this.updateInterval = setInterval(() => {
            this.updateUserData();
        }, 10000);

        // Первое обновление сразу
        this.updateUserData();
    }

    stopDataUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    async updateUserData() {
        if (!this.userAddress) return;

        try {
            // Обновляем баланс пользователя
            await this.updateUserBalance();

            // Проверяем доступ
            await this.checkUserAccess();

        } catch (error) {
            console.error('❌ Ошибка обновления данных пользователя:', error);
        }
    }

    async updateUserBalance() {
        try {
            const response = await fetch(`${this.apiUrl}/user-balance/${this.userAddress}`);
            const data = await response.json();

            if (data.success) {
                this.userBalance = data.balance;
                this.updateBalanceDisplay();
            }
        } catch (error) {
            console.error('❌ Ошибка получения баланса:', error);
        }
    }

    updateBalanceDisplay() {
        const balanceElements = ['userBalance', 'dashboardBalance'];
        const balanceInTon = (parseFloat(this.userBalance) / 1000000000).toFixed(4);

        balanceElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = `${balanceInTon} TON`;
            }
        });

        // Обновляем кнопку вывода
        const withdrawButton = document.getElementById('withdrawBalance');
        if (withdrawButton) {
            withdrawButton.disabled = parseFloat(this.userBalance) <= 0;
        }
    }

    async checkUserAccess() {
        try {
            const response = await fetch(`${this.apiUrl}/check-access`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userAddress: this.userAddress
                })
            });

            const data = await response.json();

            if (data.success) {
                this.hasAccess = data.hasAccess;
                this.updateAccessDisplay();

                if (this.hasAccess) {
                    this.showControlSection();
                }
            }
        } catch (error) {
            console.error('❌ Ошибка проверки доступа:', error);
        }
    }

    updateAccessDisplay() {
        const accessStatus = document.getElementById('accessStatus');
        if (accessStatus) {
            if (this.hasAccess) {
                accessStatus.textContent = 'Есть доступ';
                accessStatus.style.color = '#10b981';
            } else {
                accessStatus.textContent = 'Нет доступа';
                accessStatus.style.color = '#ef4444';
            }
        }
    }

    showControlSection() {
        const controlSection = document.getElementById('controlSection');
        const navigation = document.getElementById('navigation');

        if (controlSection) {
            controlSection.style.display = 'block';
        }

        if (navigation) {
            navigation.style.display = 'flex';
        }
    }

    async payForAccess() {
        if (!window.tonWallet || !window.tonWallet.connected) {
            this.showNotification('Сначала подключите кошелек', 'error');
            return;
        }

        try {
            this.showModal('Отправка платежа...');

            // Отправляем платеж через кошелек
            const result = await window.tonWallet.payForAccess(
                this.contractAddress,
                this.accessPrice
            );

            this.hideModal();

            // Показываем статус платежа
            this.showPaymentStatus('Платеж отправлен! Ожидание подтверждения...', 'loading');

            // Ждем подтверждения транзакции
            setTimeout(() => {
                this.checkPaymentConfirmation(result);
            }, 5000);

        } catch (error) {
            this.hideModal();
            this.showPaymentStatus('Ошибка платежа: ' + error.message, 'error');
            console.error('❌ Ошибка оплаты доступа:', error);
        }
    }

    async checkPaymentConfirmation(transactionResult) {
        try {
            // В реальном приложении здесь должна быть проверка транзакции
            // Пока что просто имитируем успешную оплату
            this.showPaymentStatus('Платеж подтвержден! Доступ предоставлен.', 'success');

            // Обновляем статус доступа
            setTimeout(() => {
                this.checkUserAccess();
            }, 2000);

        } catch (error) {
            this.showPaymentStatus('Ошибка подтверждения платежа', 'error');
            console.error('❌ Ошибка подтверждения платежа:', error);
        }
    }

    showPaymentStatus(message, type) {
        const statusElement = document.getElementById('paymentStatus');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `payment-status ${type}`;
            statusElement.style.display = 'block';
        }
    }

    async executeDeviceCommand(action) {
        if (!this.hasAccess) {
            this.showNotification('У вас нет доступа к управлению устройством', 'error');
            return;
        }

        try {
            this.showNotification(`Выполнение команды: ${action}`, 'info');

            const response = await fetch(`${this.apiUrl}/control-device`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userAddress: this.userAddress,
                    action: action
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Команда выполнена успешно!', 'success');
                this.updateDeviceStatus(data.deviceResponse);
            } else {
                this.showNotification('Ошибка выполнения команды: ' + data.error, 'error');
            }

        } catch (error) {
            this.showNotification('Ошибка связи с устройством', 'error');
            console.error('❌ Ошибка выполнения команды:', error);
        }
    }

    updateDeviceStatus(deviceResponse) {
        const statusElement = document.getElementById('deviceInfo');
        if (statusElement && deviceResponse) {
            const statusHtml = `
                <div class="device-response">
                    <p><strong>Последняя команда:</strong> ${deviceResponse.action}</p>
                    <p><strong>Статус:</strong> ${deviceResponse.success ? '✅ Успешно' : '❌ Ошибка'}</p>
                    <p><strong>Ответ:</strong> ${deviceResponse.response?.message || 'Нет данных'}</p>
                    <p><strong>Время:</strong> ${new Date(deviceResponse.timestamp).toLocaleString()}</p>
                    ${deviceResponse.simulated ? '<p><em>🔧 Режим симуляции</em></p>' : ''}
                </div>
            `;
            statusElement.innerHTML = statusHtml;
        }
    }

    async withdrawUserFunds() {
        if (!window.tonWallet || !window.tonWallet.connected) {
            this.showNotification('Сначала подключите кошелек', 'error');
            return;
        }

        if (parseFloat(this.userBalance) <= 0) {
            this.showNotification('У вас нет средств для вывода', 'error');
            return;
        }

        try {
            this.showModal('Отправка запроса на вывод...');

            const result = await window.tonWallet.withdrawUserFunds(this.contractAddress);

            this.hideModal();
            this.showNotification('Запрос на вывод отправлен!', 'success');

            // Обновляем баланс через несколько секунд
            setTimeout(() => {
                this.updateUserBalance();
            }, 5000);

        } catch (error) {
            this.hideModal();
            this.showNotification('Ошибка вывода средств: ' + error.message, 'error');
            console.error('❌ Ошибка вывода средств:', error);
        }
    }

    async loadDashboardData() {
        // Загружаем историю платежей и действий
        // В реальном приложении это должно загружаться с сервера
        this.showHistoryTab('payments');
    }

    showHistoryTab(tabName) {
        // Обновляем активную вкладку
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tab === tabName) {
                btn.classList.add('active');
            }
        });

        // Показываем соответствующий контент
        const historyContent = document.getElementById('historyContent');
        if (historyContent) {
            if (tabName === 'payments') {
                this.loadPaymentHistory();
            } else if (tabName === 'actions') {
                this.loadActionHistory();
            }
        }
    }

    loadPaymentHistory() {
        const historyContent = document.getElementById('historyContent');
        if (historyContent) {
            // Пример данных - в реальном приложении загружается с сервера
            historyContent.innerHTML = `
                <div class="history-item">
                    <div>
                        <strong>Оплата доступа</strong><br>
                        <small>1.0 TON</small>
                    </div>
                    <div>
                        <small>Сегодня, 14:30</small>
                    </div>
                </div>
                <div class="history-item">
                    <div>
                        <strong>Вывод средств</strong><br>
                        <small>0.5 TON</small>
                    </div>
                    <div>
                        <small>Вчера, 16:45</small>
                    </div>
                </div>
            `;
        }
    }

    loadActionHistory() {
        const historyContent = document.getElementById('historyContent');
        if (historyContent) {
            // Пример данных - в реальном приложении загружается с сервера
            historyContent.innerHTML = `
                <div class="history-item">
                    <div>
                        <strong>🚪 Открыть дверь</strong><br>
                        <small>Выполнено успешно</small>
                    </div>
                    <div>
                        <small>Сегодня, 14:35</small>
                    </div>
                </div>
                <div class="history-item">
                    <div>
                        <strong>💡 Включить свет</strong><br>
                        <small>Выполнено успешно</small>
                    </div>
                    <div>
                        <small>Сегодня, 14:32</small>
                    </div>
                </div>
            `;
        }
    }

    showModal(message) {
        const modal = document.getElementById('transactionModal');
        const statusElement = document.getElementById('transactionStatus');

        if (modal && statusElement) {
            statusElement.textContent = message;
            modal.style.display = 'block';
        }
    }

    hideModal() {
        const modal = document.getElementById('transactionModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    showNotification(message, type = 'info') {
        // Создаем уведомление
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // Добавляем в контейнер уведомлений
        const container = document.getElementById('notifications');
        if (container) {
            container.appendChild(notification);

            // Автоматически удаляем через 5 секунд
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
    }
}

// Инициализация приложения
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AccessControlApp();
});
