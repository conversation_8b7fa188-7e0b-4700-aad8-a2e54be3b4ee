const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
    constructor() {
        this.db = null;
        this.dbPath = process.env.DB_PATH || path.join(__dirname, '../database.sqlite');
    }
    
    async initialize() {
        return new Promise((resolve, reject) => {
            console.log('🗄️  Инициализация базы данных...');
            
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('❌ Ошибка подключения к БД:', err.message);
                    reject(err);
                    return;
                }
                
                console.log('✅ База данных подключена');
                this.createTables()
                    .then(() => resolve())
                    .catch(reject);
            });
        });
    }
    
    async createTables() {
        return new Promise((resolve, reject) => {
            const queries = [
                // Таблица платежей
                `CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_address TEXT NOT NULL,
                    transaction_hash TEXT UNIQUE NOT NULL,
                    amount TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'confirmed'
                )`,
                
                // Таблица действий с устройством
                `CREATE TABLE IF NOT EXISTS device_actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_address TEXT NOT NULL,
                    action TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN NOT NULL,
                    response TEXT
                )`,
                
                // Таблица пользователей
                `CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    address TEXT UNIQUE NOT NULL,
                    first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                    total_payments TEXT DEFAULT '0'
                )`,
                
                // Индексы для оптимизации
                `CREATE INDEX IF NOT EXISTS idx_payments_user ON payments(user_address)`,
                `CREATE INDEX IF NOT EXISTS idx_payments_hash ON payments(transaction_hash)`,
                `CREATE INDEX IF NOT EXISTS idx_actions_user ON device_actions(user_address)`,
                `CREATE INDEX IF NOT EXISTS idx_users_address ON users(address)`
            ];
            
            let completed = 0;
            const total = queries.length;
            
            queries.forEach((query) => {
                this.db.run(query, (err) => {
                    if (err) {
                        console.error('❌ Ошибка создания таблицы:', err.message);
                        reject(err);
                        return;
                    }
                    
                    completed++;
                    if (completed === total) {
                        console.log('✅ Таблицы базы данных созданы');
                        resolve();
                    }
                });
            });
        });
    }
    
    async isConnected() {
        return this.db !== null;
    }
    
    async savePayment(paymentData) {
        return new Promise((resolve, reject) => {
            const { userAddress, transactionHash, amount, timestamp } = paymentData;
            
            // Сначала добавляем/обновляем пользователя
            this.upsertUser(userAddress, amount)
                .then(() => {
                    // Затем сохраняем платеж
                    const query = `
                        INSERT INTO payments (user_address, transaction_hash, amount, timestamp)
                        VALUES (?, ?, ?, ?)
                    `;
                    
                    this.db.run(query, [userAddress, transactionHash, amount, timestamp], function(err) {
                        if (err) {
                            if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                                // Платеж уже существует
                                resolve({ id: null, exists: true });
                            } else {
                                reject(err);
                            }
                            return;
                        }
                        
                        console.log(`💰 Платеж сохранен: ${amount} от ${userAddress}`);
                        resolve({ id: this.lastID, exists: false });
                    });
                })
                .catch(reject);
        });
    }
    
    async upsertUser(userAddress, paymentAmount = '0') {
        return new Promise((resolve, reject) => {
            // Проверяем, существует ли пользователь
            const selectQuery = `SELECT total_payments FROM users WHERE address = ?`;
            
            this.db.get(selectQuery, [userAddress], (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }
                
                if (row) {
                    // Пользователь существует, обновляем
                    const currentTotal = BigInt(row.total_payments || '0');
                    const newTotal = currentTotal + BigInt(paymentAmount);
                    
                    const updateQuery = `
                        UPDATE users 
                        SET last_activity = CURRENT_TIMESTAMP, total_payments = ?
                        WHERE address = ?
                    `;
                    
                    this.db.run(updateQuery, [newTotal.toString(), userAddress], (err) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve();
                        }
                    });
                } else {
                    // Новый пользователь
                    const insertQuery = `
                        INSERT INTO users (address, total_payments)
                        VALUES (?, ?)
                    `;
                    
                    this.db.run(insertQuery, [userAddress, paymentAmount], (err) => {
                        if (err) {
                            reject(err);
                        } else {
                            console.log(`👤 Новый пользователь: ${userAddress}`);
                            resolve();
                        }
                    });
                }
            });
        });
    }
    
    async logDeviceAction(actionData) {
        return new Promise((resolve, reject) => {
            const { userAddress, action, timestamp, success, response } = actionData;
            
            const query = `
                INSERT INTO device_actions (user_address, action, timestamp, success, response)
                VALUES (?, ?, ?, ?, ?)
            `;
            
            this.db.run(query, [
                userAddress, 
                action, 
                timestamp, 
                success ? 1 : 0, 
                response || null
            ], function(err) {
                if (err) {
                    reject(err);
                    return;
                }
                
                console.log(`🎮 Действие записано: ${action} от ${userAddress}`);
                resolve({ id: this.lastID });
            });
        });
    }
    
    async getUserPayments(userAddress, limit = 50) {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT * FROM payments 
                WHERE user_address = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            `;
            
            this.db.all(query, [userAddress, limit], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }
    
    async getUserActions(userAddress, limit = 50) {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT * FROM device_actions 
                WHERE user_address = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            `;
            
            this.db.all(query, [userAddress, limit], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }
    
    async getStats() {
        return new Promise((resolve, reject) => {
            const queries = {
                totalUsers: `SELECT COUNT(*) as count FROM users`,
                totalPayments: `SELECT COUNT(*) as count, SUM(CAST(amount AS INTEGER)) as total FROM payments`,
                totalActions: `SELECT COUNT(*) as count FROM device_actions WHERE success = 1`
            };
            
            const results = {};
            let completed = 0;
            const total = Object.keys(queries).length;
            
            Object.entries(queries).forEach(([key, query]) => {
                this.db.get(query, (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    results[key] = row;
                    completed++;
                    
                    if (completed === total) {
                        resolve(results);
                    }
                });
            });
        });
    }
    
    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Ошибка закрытия БД:', err.message);
                    } else {
                        console.log('🗄️  База данных закрыта');
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = Database;
