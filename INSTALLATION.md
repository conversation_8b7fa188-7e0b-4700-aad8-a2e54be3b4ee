# 📦 Инструкция по установке TON Access Control System

## 🔧 Предварительные требования

### 1. Установка Node.js

**Windows:**
1. Скачайте Node.js с официального сайта: https://nodejs.org/
2. Выберите LTS версию (рекомендуется)
3. Запустите установщик и следуйте инструкциям
4. Перезапустите командную строку

**Проверка установки:**
```bash
node --version
npm --version
```

### 2. Установка TON Development Environment (опционально)

Для компиляции смарт-контрактов нужен FunC компилятор:

**Windows:**
1. Установите WSL2 (Windows Subsystem for Linux)
2. В WSL установите TON tools:
```bash
wget https://github.com/ton-blockchain/ton/releases/latest/download/ton-linux-x86_64.zip
unzip ton-linux-x86_64.zip
sudo mv ton-linux-x86_64/* /usr/local/bin/
```

**Альтернативно:** Используйте предкомпилированные контракты (включены в проект)

## 🚀 Установка проекта

### Шаг 1: Установка зависимостей

```bash
npm install
```

### Шаг 2: Настройка кошелька

1. **Создайте файл с мнемонической фразой:**
```bash
copy wallet.mnemonic.example wallet.mnemonic
```

2. **Отредактируйте `wallet.mnemonic`** и вставьте вашу мнемоническую фразу из 24 слов

### Шаг 3: Настройка переменных окружения

Отредактируйте файл `.env`:

```env
# TON Network Configuration
TON_NETWORK=testnet
TON_API_KEY=your_ton_api_key_here

# Smart Contract (будет заполнено автоматически после развертывания)
CONTRACT_ADDRESS=
OWNER_WALLET=your_wallet_address_here

# Server Configuration
PORT=8080
HOST=localhost

# Payment Settings
ACCESS_PRICE=1000000000  # 1 TON в nanotons
SERVICE_FEE_PERCENT=10   # 10% комиссия сервиса
```

### Шаг 4: Получение TON API ключа

1. Перейдите на https://toncenter.com/
2. Зарегистрируйтесь и получите API ключ
3. Вставьте ключ в `.env` файл

## 🔗 Развертывание смарт-контракта

### Вариант 1: Автоматическое развертывание

```bash
npm run deploy-contract
```

### Вариант 2: Ручное развертывание

1. **Компиляция контракта:**
```bash
npm run compile-contract
```

2. **Развертывание:**
```bash
npm run deploy-contract
```

**Примечание:** Для развертывания нужно иметь минимум 1 TON на кошельке

## 🏃‍♂️ Запуск системы

### Режим разработки:
```bash
npm run dev
```

### Продакшен:
```bash
npm start
```

### Статический сервер (только фронтенд):
```bash
npm run serve
```

## 🌐 Доступ к приложению

После запуска сервера откройте браузер и перейдите по адресу:
**http://localhost:8080**

## 🔍 Проверка работы

### 1. Проверка статуса сервера:
```bash
curl http://localhost:8080/api/status
```

### 2. Проверка информации о контракте:
```bash
curl http://localhost:8080/api/contract-info
```

## 🐛 Устранение проблем

### Проблема: "npm не найден"
**Решение:** Установите Node.js с официального сайта

### Проблема: "Контракт не развертывается"
**Решение:** 
- Проверьте баланс кошелька (нужно минимум 1 TON)
- Убедитесь, что мнемоническая фраза корректна
- Проверьте TON_API_KEY в .env

### Проблема: "Ошибка подключения к базе данных"
**Решение:** База данных SQLite создается автоматически, проверьте права доступа к папке

### Проблема: "Кошелек не подключается"
**Решение:**
- Используйте поддерживаемый кошелек (Tonkeeper, TON Wallet)
- Убедитесь, что находитесь в правильной сети (testnet/mainnet)

## 📱 Поддерживаемые кошельки

- **Tonkeeper** (рекомендуется)
- **TON Wallet**
- **OpenMask**
- **MyTonWallet**

## 🔒 Безопасность

### ⚠️ ВАЖНО:

1. **Никогда не делитесь файлом `wallet.mnemonic`**
2. **Используйте тестовую сеть для разработки**
3. **Делайте резервные копии мнемонической фразы**
4. **В продакшене используйте HTTPS**

## 📞 Поддержка

Если возникли проблемы:

1. Проверьте логи в консоли
2. Убедитесь, что все зависимости установлены
3. Проверьте настройки в .env файле
4. Убедитесь, что порт 8080 свободен

## 🎯 Следующие шаги

После успешной установки:

1. **Подключите TON кошелек** в веб-интерфейсе
2. **Пополните кошелек** тестовыми TON (для testnet)
3. **Оплатите доступ** и протестируйте управление устройством
4. **Изучите API** для интеграции с реальными устройствами

---

**Готово! Ваша система управления доступом на TON готова к использованию! 🎉**
