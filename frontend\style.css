/* Основные стили */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* Заголовок */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #4f46e5;
    margin-bottom: 5px;
}

.logo p {
    color: #6b7280;
    font-size: 0.9rem;
}

.wallet-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.wallet-address {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.8rem;
    color: #6b7280;
    background: #f3f4f6;
    padding: 8px 12px;
    border-radius: 8px;
}

/* Кнопки */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background: #4338ca;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 600;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Карточки */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.status-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.status-label {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 8px;
}

.status-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}

/* Секции */
.payment-section,
.control-section,
.dashboard-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.payment-section h2,
.control-section h2,
.dashboard-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
}

.payment-info {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.payment-info p {
    margin-bottom: 8px;
    color: #4b5563;
}

.payment-status {
    margin-top: 20px;
    padding: 15px;
    border-radius: 12px;
    text-align: center;
    font-weight: 500;
}

.payment-status.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.payment-status.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.payment-status.loading {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

/* Управление устройством */
.access-info {
    background: #d1fae5;
    border: 1px solid #a7f3d0;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    color: #065f46;
}

.control-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.control-btn {
    background: #f8fafc;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    border-color: #4f46e5;
    background: #f0f9ff;
    transform: translateY(-2px);
}

.control-btn .icon {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.control-btn .name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.control-btn .description {
    font-size: 0.8rem;
    color: #6b7280;
}

.device-status {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
}

/* Личный кабинет */
.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    margin-bottom: 30px;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 15px 0;
}

.history-section {
    margin-top: 30px;
}

.history-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    border: none;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #4f46e5;
    color: white;
}

.history-content {
    min-height: 200px;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.history-item:last-child {
    border-bottom: none;
}

/* Навигация */
.navigation {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 10px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 10px;
}

.nav-btn {
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #6b7280;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-btn.active {
    background: #4f46e5;
    color: white;
}

/* Модальные окна */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 20px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Уведомления */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.notification {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #4f46e5;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left-color: #10b981;
}

.notification.error {
    border-left-color: #ef4444;
}

.notification.warning {
    border-left-color: #f59e0b;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Адаптивность */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .status-card {
        grid-template-columns: 1fr;
    }
    
    .control-buttons {
        grid-template-columns: 1fr;
    }
    
    .navigation {
        bottom: 10px;
        left: 10px;
        right: 10px;
        transform: none;
    }
}
