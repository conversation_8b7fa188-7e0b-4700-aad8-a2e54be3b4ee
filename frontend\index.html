<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TON Access Control - Управление доступом</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Заголовок -->
        <header class="header">
            <div class="logo">
                <h1>🔐 TON Access Control</h1>
                <p>Система управления доступом на блокчейне TON</p>
            </div>
            <div class="wallet-info" id="walletInfo">
                <button id="connectWallet" class="btn btn-primary">
                    Подключить кошелек
                </button>
            </div>
        </header>

        <!-- Основной контент -->
        <main class="main">
            <!-- Статус подключения -->
            <div class="status-card" id="statusCard">
                <div class="status-item">
                    <span class="status-label">Кошелек:</span>
                    <span class="status-value" id="walletStatus">Не подключен</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Доступ:</span>
                    <span class="status-value" id="accessStatus">Нет доступа</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Баланс:</span>
                    <span class="status-value" id="userBalance">0 TON</span>
                </div>
            </div>

            <!-- Секция оплаты -->
            <div class="payment-section" id="paymentSection" style="display: none;">
                <h2>💰 Оплата доступа</h2>
                <div class="payment-info">
                    <p>Стоимость доступа: <strong>1 TON</strong></p>
                    <p>Время доступа: <strong>1 час</strong></p>
                    <p>Комиссия сервиса: <strong>10%</strong></p>
                </div>
                <button id="payForAccess" class="btn btn-success btn-large">
                    Оплатить доступ (1 TON)
                </button>
                <div id="paymentStatus" class="payment-status"></div>
            </div>

            <!-- Панель управления устройством -->
            <div class="control-section" id="controlSection" style="display: none;">
                <h2>🎮 Управление устройством</h2>
                <div class="access-info">
                    <p>✅ У вас есть доступ к управлению устройством</p>
                    <p id="accessTime">Доступ действует до: <span id="accessUntil">--</span></p>
                </div>
                
                <div class="device-controls">
                    <h3>Доступные команды:</h3>
                    <div class="control-buttons" id="controlButtons">
                        <!-- Кнопки будут добавлены динамически -->
                    </div>
                </div>
                
                <div class="device-status" id="deviceStatus">
                    <h3>Статус устройства:</h3>
                    <div id="deviceInfo">Загрузка...</div>
                </div>
            </div>

            <!-- Личный кабинет -->
            <div class="dashboard-section" id="dashboardSection" style="display: none;">
                <h2>👤 Личный кабинет</h2>
                
                <div class="balance-card">
                    <h3>💰 Ваш баланс</h3>
                    <div class="balance-amount" id="dashboardBalance">0 TON</div>
                    <button id="withdrawBalance" class="btn btn-warning" disabled>
                        Вывести средства
                    </button>
                </div>
                
                <div class="history-section">
                    <h3>📊 История операций</h3>
                    <div class="history-tabs">
                        <button class="tab-btn active" data-tab="payments">Платежи</button>
                        <button class="tab-btn" data-tab="actions">Действия</button>
                    </div>
                    <div class="history-content" id="historyContent">
                        <!-- История будет загружена динамически -->
                    </div>
                </div>
            </div>
        </main>

        <!-- Навигация -->
        <nav class="navigation" id="navigation" style="display: none;">
            <button class="nav-btn active" data-section="control">
                🎮 Управление
            </button>
            <button class="nav-btn" data-section="dashboard">
                👤 Кабинет
            </button>
        </nav>

        <!-- Модальные окна -->
        <div class="modal" id="transactionModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Обработка транзакции</h3>
                    <button class="modal-close" id="closeModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="loading-spinner"></div>
                    <p id="transactionStatus">Ожидание подтверждения транзакции...</p>
                </div>
            </div>
        </div>

        <!-- Уведомления -->
        <div class="notifications" id="notifications"></div>
    </div>

    <!-- Подключение скриптов -->
    <script src="https://unpkg.com/@tonconnect/ui@latest/dist/tonconnect-ui.min.js"></script>
    <script src="ton-wallet.js"></script>
    <script src="app.js"></script>
</body>
</html>
