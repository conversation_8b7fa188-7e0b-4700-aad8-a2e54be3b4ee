// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`serializeDict should build prefix tree 1`] = `
Cell {
  "_depths": [
    2,
    2,
    2,
    2,
  ],
  "_hashes": [
    {
      "data": [
        200,
        192,
        202,
        112,
        113,
        234,
        191,
        24,
        167,
        26,
        220,
        187,
        57,
        141,
        29,
        33,
        100,
        177,
        55,
        139,
        154,
        231,
        12,
        0,
        81,
        0,
        73,
        251,
        134,
        90,
        236,
        106,
      ],
      "type": "Buffer",
    },
    {
      "data": [
        200,
        192,
        202,
        112,
        113,
        234,
        191,
        24,
        167,
        26,
        220,
        187,
        57,
        141,
        29,
        33,
        100,
        177,
        55,
        139,
        154,
        231,
        12,
        0,
        81,
        0,
        73,
        251,
        134,
        90,
        236,
        106,
      ],
      "type": "Buffer",
    },
    {
      "data": [
        200,
        192,
        202,
        112,
        113,
        234,
        191,
        24,
        167,
        26,
        220,
        187,
        57,
        141,
        29,
        33,
        100,
        177,
        55,
        139,
        154,
        231,
        12,
        0,
        81,
        0,
        73,
        251,
        134,
        90,
        236,
        106,
      ],
      "type": "Buffer",
    },
    {
      "data": [
        200,
        192,
        202,
        112,
        113,
        234,
        191,
        24,
        167,
        26,
        220,
        187,
        57,
        141,
        29,
        33,
        100,
        177,
        55,
        139,
        154,
        231,
        12,
        0,
        81,
        0,
        73,
        251,
        134,
        90,
        236,
        106,
      ],
      "type": "Buffer",
    },
  ],
  "beginParse": [Function],
  "bits": BitString {
    "_data": {
      "data": [
        200,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
      ],
      "type": "Buffer",
    },
    "_length": 8,
    "_offset": 0,
    Symbol(nodejs.util.inspect.custom): [Function],
  },
  "depth": [Function],
  "equals": [Function],
  "hash": [Function],
  "level": [Function],
  "mask": LevelMask {
    "_hashCount": 1,
    "_hashIndex": 0,
    "_mask": 0,
  },
  "refs": [
    Cell {
      "_depths": [
        1,
        1,
        1,
        1,
      ],
      "_hashes": [
        {
          "data": [
            198,
            21,
            202,
            139,
            21,
            128,
            158,
            157,
            177,
            113,
            25,
            230,
            107,
            125,
            195,
            36,
            181,
            187,
            210,
            72,
            200,
            202,
            242,
            254,
            99,
            86,
            103,
            110,
            125,
            107,
            128,
            14,
          ],
          "type": "Buffer",
        },
        {
          "data": [
            198,
            21,
            202,
            139,
            21,
            128,
            158,
            157,
            177,
            113,
            25,
            230,
            107,
            125,
            195,
            36,
            181,
            187,
            210,
            72,
            200,
            202,
            242,
            254,
            99,
            86,
            103,
            110,
            125,
            107,
            128,
            14,
          ],
          "type": "Buffer",
        },
        {
          "data": [
            198,
            21,
            202,
            139,
            21,
            128,
            158,
            157,
            177,
            113,
            25,
            230,
            107,
            125,
            195,
            36,
            181,
            187,
            210,
            72,
            200,
            202,
            242,
            254,
            99,
            86,
            103,
            110,
            125,
            107,
            128,
            14,
          ],
          "type": "Buffer",
        },
        {
          "data": [
            198,
            21,
            202,
            139,
            21,
            128,
            158,
            157,
            177,
            113,
            25,
            230,
            107,
            125,
            195,
            36,
            181,
            187,
            210,
            72,
            200,
            202,
            242,
            254,
            99,
            86,
            103,
            110,
            125,
            107,
            128,
            14,
          ],
          "type": "Buffer",
        },
      ],
      "beginParse": [Function],
      "bits": BitString {
        "_data": {
          "data": [
            96,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
          ],
          "type": "Buffer",
        },
        "_length": 6,
        "_offset": 0,
        Symbol(nodejs.util.inspect.custom): [Function],
      },
      "depth": [Function],
      "equals": [Function],
      "hash": [Function],
      "level": [Function],
      "mask": LevelMask {
        "_hashCount": 1,
        "_hashIndex": 0,
        "_mask": 0,
      },
      "refs": [
        Cell {
          "_depths": [
            0,
            0,
            0,
            0,
          ],
          "_hashes": [
            {
              "data": [
                197,
                207,
                54,
                140,
                222,
                41,
                210,
                150,
                211,
                212,
                72,
                214,
                20,
                13,
                60,
                44,
                248,
                37,
                231,
                85,
                221,
                213,
                43,
                179,
                184,
                189,
                151,
                194,
                42,
                220,
                156,
                119,
              ],
              "type": "Buffer",
            },
            {
              "data": [
                197,
                207,
                54,
                140,
                222,
                41,
                210,
                150,
                211,
                212,
                72,
                214,
                20,
                13,
                60,
                44,
                248,
                37,
                231,
                85,
                221,
                213,
                43,
                179,
                184,
                189,
                151,
                194,
                42,
                220,
                156,
                119,
              ],
              "type": "Buffer",
            },
            {
              "data": [
                197,
                207,
                54,
                140,
                222,
                41,
                210,
                150,
                211,
                212,
                72,
                214,
                20,
                13,
                60,
                44,
                248,
                37,
                231,
                85,
                221,
                213,
                43,
                179,
                184,
                189,
                151,
                194,
                42,
                220,
                156,
                119,
              ],
              "type": "Buffer",
            },
            {
              "data": [
                197,
                207,
                54,
                140,
                222,
                41,
                210,
                150,
                211,
                212,
                72,
                214,
                20,
                13,
                60,
                44,
                248,
                37,
                231,
                85,
                221,
                213,
                43,
                179,
                184,
                189,
                151,
                194,
                42,
                220,
                156,
                119,
              ],
              "type": "Buffer",
            },
          ],
          "beginParse": [Function],
          "bits": BitString {
            "_data": {
              "data": [
                166,
                128,
                84,
                128,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
              "type": "Buffer",
            },
            "_length": 25,
            "_offset": 0,
            Symbol(nodejs.util.inspect.custom): [Function],
          },
          "depth": [Function],
          "equals": [Function],
          "hash": [Function],
          "level": [Function],
          "mask": LevelMask {
            "_hashCount": 1,
            "_hashIndex": 0,
            "_mask": 0,
          },
          "refs": [],
          "type": -1,
          Symbol(nodejs.util.inspect.custom): [Function],
        },
        Cell {
          "_depths": [
            0,
            0,
            0,
            0,
          ],
          "_hashes": [
            {
              "data": [
                17,
                237,
                44,
                82,
                219,
                17,
                76,
                155,
                1,
                58,
                105,
                64,
                114,
                206,
                14,
                111,
                188,
                12,
                5,
                103,
                232,
                172,
                83,
                89,
                156,
                136,
                231,
                106,
                229,
                30,
                59,
                189,
              ],
              "type": "Buffer",
            },
            {
              "data": [
                17,
                237,
                44,
                82,
                219,
                17,
                76,
                155,
                1,
                58,
                105,
                64,
                114,
                206,
                14,
                111,
                188,
                12,
                5,
                103,
                232,
                172,
                83,
                89,
                156,
                136,
                231,
                106,
                229,
                30,
                59,
                189,
              ],
              "type": "Buffer",
            },
            {
              "data": [
                17,
                237,
                44,
                82,
                219,
                17,
                76,
                155,
                1,
                58,
                105,
                64,
                114,
                206,
                14,
                111,
                188,
                12,
                5,
                103,
                232,
                172,
                83,
                89,
                156,
                136,
                231,
                106,
                229,
                30,
                59,
                189,
              ],
              "type": "Buffer",
            },
            {
              "data": [
                17,
                237,
                44,
                82,
                219,
                17,
                76,
                155,
                1,
                58,
                105,
                64,
                114,
                206,
                14,
                111,
                188,
                12,
                5,
                103,
                232,
                172,
                83,
                89,
                156,
                136,
                231,
                106,
                229,
                30,
                59,
                189,
              ],
              "type": "Buffer",
            },
          ],
          "beginParse": [Function],
          "bits": BitString {
            "_data": {
              "data": [
                160,
                128,
                144,
                128,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
              "type": "Buffer",
            },
            "_length": 25,
            "_offset": 0,
            Symbol(nodejs.util.inspect.custom): [Function],
          },
          "depth": [Function],
          "equals": [Function],
          "hash": [Function],
          "level": [Function],
          "mask": LevelMask {
            "_hashCount": 1,
            "_hashIndex": 0,
            "_mask": 0,
          },
          "refs": [],
          "type": -1,
          Symbol(nodejs.util.inspect.custom): [Function],
        },
      ],
      "type": -1,
      Symbol(nodejs.util.inspect.custom): [Function],
    },
    Cell {
      "_depths": [
        0,
        0,
        0,
        0,
      ],
      "_hashes": [
        {
          "data": [
            86,
            169,
            104,
            105,
            218,
            207,
            105,
            9,
            200,
            18,
            16,
            247,
            18,
            82,
            117,
            52,
            158,
            194,
            83,
            161,
            67,
            196,
            178,
            192,
            1,
            199,
            248,
            214,
            131,
            50,
            132,
            42,
          ],
          "type": "Buffer",
        },
        {
          "data": [
            86,
            169,
            104,
            105,
            218,
            207,
            105,
            9,
            200,
            18,
            16,
            247,
            18,
            82,
            117,
            52,
            158,
            194,
            83,
            161,
            67,
            196,
            178,
            192,
            1,
            199,
            248,
            214,
            131,
            50,
            132,
            42,
          ],
          "type": "Buffer",
        },
        {
          "data": [
            86,
            169,
            104,
            105,
            218,
            207,
            105,
            9,
            200,
            18,
            16,
            247,
            18,
            82,
            117,
            52,
            158,
            194,
            83,
            161,
            67,
            196,
            178,
            192,
            1,
            199,
            248,
            214,
            131,
            50,
            132,
            42,
          ],
          "type": "Buffer",
        },
        {
          "data": [
            86,
            169,
            104,
            105,
            218,
            207,
            105,
            9,
            200,
            18,
            16,
            247,
            18,
            82,
            117,
            52,
            158,
            194,
            83,
            161,
            67,
            196,
            178,
            192,
            1,
            199,
            248,
            214,
            131,
            50,
            132,
            42,
          ],
          "type": "Buffer",
        },
      ],
      "beginParse": [Function],
      "bits": BitString {
        "_data": {
          "data": [
            190,
            253,
            242,
            16,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
          ],
          "type": "Buffer",
        },
        "_length": 28,
        "_offset": 0,
        Symbol(nodejs.util.inspect.custom): [Function],
      },
      "depth": [Function],
      "equals": [Function],
      "hash": [Function],
      "level": [Function],
      "mask": LevelMask {
        "_hashCount": 1,
        "_hashIndex": 0,
        "_mask": 0,
      },
      "refs": [],
      "type": -1,
      Symbol(nodejs.util.inspect.custom): [Function],
    },
  ],
  "type": -1,
  Symbol(nodejs.util.inspect.custom): [Function],
}
`;
