# 🔐 TON Access Control System

Система управления доступом к устройствам с накопительным смарт-контрактом на блокчейне TON.

## 🎯 Описание проекта

Эта система позволяет:
- **Оплачивать доступ** к устройствам через TON кошелек
- **Управлять устройствами** после оплаты (открытие дверей, включение света, запуск моторов и т.д.)
- **Накапливать средства** для каждого пользователя в смарт-контракте
- **Выводить накопленные средства** через личный кабинет
- **Получать комиссию сервиса** с каждого платежа

## 🏗️ Архитектура системы

### Компоненты:
1. **Смарт-контракт (FunC)** - накопительный контракт на TON
2. **Backend (Node.js)** - API сервер для взаимодействия с контрактом
3. **Frontend (HTML/JS)** - веб-интерфейс с интеграцией TON кошелька
4. **Database (SQLite)** - хранение истории операций
5. **Device API** - симуляция управления устройствами

## 🚀 Быстрый старт

### Предварительные требования

1. **Node.js** (версия 16 или выше)
2. **TON Development Environment** (для компиляции контрактов)
3. **TON кошелек** (Tonkeeper, TON Wallet и др.)

### Установка

1. **Клонируйте проект и установите зависимости:**
```bash
npm install
```

2. **Настройте переменные окружения:**
Отредактируйте файл `.env` и укажите ваши настройки:
```env
TON_NETWORK=testnet
TON_API_KEY=your_ton_api_key_here
OWNER_WALLET=your_wallet_address_here
ACCESS_PRICE=1000000000  # 1 TON
SERVICE_FEE_PERCENT=10   # 10% комиссия
```

3. **Создайте файл с мнемонической фразой:**
```bash
echo "word1 word2 word3 ... word24" > wallet.mnemonic
```

### Развертывание

1. **Скомпилируйте смарт-контракт:**
```bash
npm run compile-contract
```

2. **Разверните контракт в сети TON:**
```bash
npm run deploy-contract
```

3. **Запустите сервер:**
```bash
npm start
```

4. **Откройте приложение:**
Перейдите по адресу: http://localhost:8080

## 📱 Использование

### Для пользователей:

1. **Подключите TON кошелек** через кнопку "Подключить кошелек"
2. **Оплатите доступ** (1 TON) для получения права управления устройством
3. **Управляйте устройством** через доступные кнопки
4. **Просматривайте баланс** и выводите накопленные средства в личном кабинете

### Для владельца сервиса:

1. **Мониторьте платежи** через API или базу данных
2. **Выводите комиссию** с помощью функции `withdrawService`
3. **Просматривайте статистику** использования системы

## 🔧 API Endpoints

### Основные маршруты:

- `GET /api/status` - Статус системы
- `GET /api/contract-info` - Информация о контракте
- `POST /api/check-payment` - Проверка платежа
- `POST /api/check-access` - Проверка доступа пользователя
- `POST /api/control-device` - Управление устройством
- `GET /api/user-balance/:address` - Баланс пользователя
- `POST /api/withdraw-user` - Вывод средств пользователя

## 💰 Экономическая модель

### Стоимость доступа: **1 TON**
### Время доступа: **1 час**
### Комиссия сервиса: **10%**

**Распределение платежа:**
- 90% → накапливается на балансе пользователя
- 10% → комиссия сервиса

## 🔒 Смарт-контракт

### Основные функции:

- `OP_PAY_ACCESS (1)` - Оплата доступа
- `OP_WITHDRAW_USER (2)` - Вывод средств пользователя
- `OP_WITHDRAW_SERVICE (3)` - Вывод комиссии сервиса

### Get-методы:

- `get_user_balance_method()` - Баланс пользователя
- `get_user_access()` - Проверка доступа
- `get_service_balance()` - Баланс сервиса
- `get_owner_address()` - Адрес владельца

## 🎮 Управление устройствами

### Доступные команды:

- 🚪 **Открыть дверь** - Открывает главную дверь на 5 секунд
- 💡 **Включить свет** - Включает основное освещение
- ⚙️ **Запустить мотор** - Запускает главный мотор
- 🚰 **Активировать насос** - Включает водяной насос
- 🚧 **Разблокировать ворота** - Разблокирует ворота на 10 секунд

*Примечание: В текущей версии используется симуляция устройств. Для подключения реальных устройств измените настройки в `backend/device-api.js`*

## 🗄️ База данных

### Таблицы:

- **payments** - История платежей
- **device_actions** - История действий с устройствами
- **users** - Информация о пользователях

## 🔧 Разработка

### Структура проекта:

```
├── contracts/           # Смарт-контракты FunC
├── scripts/            # Скрипты компиляции и развертывания
├── backend/            # Backend сервер
├── frontend/           # Frontend приложение
├── build/              # Скомпилированные контракты
├── package.json        # Зависимости проекта
├── .env               # Переменные окружения
└── README.md          # Документация
```

### Команды разработки:

```bash
npm run dev              # Запуск в режиме разработки
npm run compile-contract # Компиляция контракта
npm run deploy-contract  # Развертывание контракта
npm test                # Запуск тестов
npm run serve           # Запуск статического сервера
```

## 🌐 Сети

### Testnet (по умолчанию):
- Endpoint: `https://testnet.toncenter.com/api/v2/jsonRPC`
- Для тестирования и разработки

### Mainnet:
- Endpoint: `https://toncenter.com/api/v2/jsonRPC`
- Для продакшена (измените `TON_NETWORK=mainnet` в .env)

## 🔐 Безопасность

### Рекомендации:

1. **Храните мнемоническую фразу в безопасности**
2. **Используйте HTTPS в продакшене**
3. **Регулярно обновляйте зависимости**
4. **Мониторьте транзакции контракта**
5. **Делайте резервные копии базы данных**

## 🐛 Устранение неполадок

### Частые проблемы:

1. **Контракт не развертывается:**
   - Проверьте баланс кошелька (нужно минимум 1 TON)
   - Убедитесь, что мнемоническая фраза корректна

2. **Кошелек не подключается:**
   - Проверьте, что используете поддерживаемый кошелек
   - Убедитесь, что находитесь в правильной сети (testnet/mainnet)

3. **Устройство не отвечает:**
   - Проверьте настройки `DEVICE_API_URL` в .env
   - В режиме симуляции это нормально

## 📞 Поддержка

Если у вас возникли вопросы или проблемы:

1. Проверьте логи сервера в консоли
2. Убедитесь, что все зависимости установлены
3. Проверьте настройки в файле .env

## 📄 Лицензия

MIT License - см. файл LICENSE для подробностей.

---

**Создано для демонстрации возможностей TON блокчейна в IoT и системах управления доступом.**
