const { TonClient, Address, Cell, beginCell } = require('@ton/ton');
const { mnemonicToPrivateKey } = require('@ton/crypto');
const fs = require('fs');
const path = require('path');

class TonClientWrapper {
    constructor() {
        this.client = null;
        this.contractAddress = null;
        this.ownerWallet = null;
        this.keyPair = null;
    }
    
    async initialize() {
        try {
            console.log('🔗 Инициализация TON клиента...');
            
            // Подключение к сети
            const endpoint = process.env.TON_NETWORK === 'mainnet' 
                ? 'https://toncenter.com/api/v2/jsonRPC'
                : 'https://testnet.toncenter.com/api/v2/jsonRPC';
                
            this.client = new TonClient({
                endpoint: endpoint,
                apiKey: process.env.TON_API_KEY
            });
            
            // Загружаем адрес контракта
            this.contractAddress = process.env.CONTRACT_ADDRESS;
            if (!this.contractAddress) {
                throw new Error('CONTRACT_ADDRESS не установлен в .env файле');
            }
            
            // Загружаем кошелек владельца
            const mnemonicPath = path.join(__dirname, '../wallet.mnemonic');
            if (fs.existsSync(mnemonicPath)) {
                const mnemonic = fs.readFileSync(mnemonicPath, 'utf8').trim().split(' ');
                this.keyPair = await mnemonicToPrivateKey(mnemonic);
            }
            
            console.log('✅ TON клиент инициализирован');
            console.log(`📍 Адрес контракта: ${this.contractAddress}`);
            
        } catch (error) {
            console.error('❌ Ошибка инициализации TON клиента:', error.message);
            throw error;
        }
    }
    
    async isContractDeployed() {
        try {
            if (!this.contractAddress) return false;
            
            const address = Address.parse(this.contractAddress);
            const state = await this.client.getContractState(address);
            
            return state.state === 'active';
        } catch (error) {
            console.error('Ошибка проверки контракта:', error.message);
            return false;
        }
    }
    
    async getContractInfo() {
        try {
            const address = Address.parse(this.contractAddress);
            const state = await this.client.getContractState(address);
            
            return {
                address: this.contractAddress,
                state: state.state,
                balance: state.balance,
                lastTransactionId: state.lastTransactionId
            };
        } catch (error) {
            throw new Error(`Ошибка получения информации о контракте: ${error.message}`);
        }
    }
    
    async checkPayment(transactionHash, userAddress) {
        try {
            // Получаем транзакцию по хешу
            const transactions = await this.client.getTransactions(
                Address.parse(this.contractAddress),
                { limit: 100 }
            );
            
            // Ищем транзакцию с нужным хешем
            const transaction = transactions.find(tx => 
                tx.hash().toString('hex') === transactionHash
            );
            
            if (!transaction) {
                return {
                    success: false,
                    error: 'Транзакция не найдена'
                };
            }
            
            // Проверяем, что транзакция от нужного пользователя
            const inMsg = transaction.inMessage;
            if (!inMsg || !inMsg.source) {
                return {
                    success: false,
                    error: 'Некорректная транзакция'
                };
            }
            
            const senderAddress = inMsg.source.toString();
            if (senderAddress !== userAddress) {
                return {
                    success: false,
                    error: 'Адрес отправителя не совпадает'
                };
            }
            
            // Проверяем сумму
            const amount = inMsg.value.coins;
            const requiredAmount = BigInt(process.env.ACCESS_PRICE || '1000000000');
            
            if (amount < requiredAmount) {
                return {
                    success: false,
                    error: 'Недостаточная сумма платежа'
                };
            }
            
            return {
                success: true,
                amount: amount.toString(),
                transactionHash: transactionHash,
                userAddress: userAddress
            };
            
        } catch (error) {
            return {
                success: false,
                error: `Ошибка проверки платежа: ${error.message}`
            };
        }
    }
    
    async checkUserAccess(userAddress) {
        try {
            const address = Address.parse(this.contractAddress);
            const userAddr = Address.parse(userAddress);
            
            // Вызываем get-метод контракта
            const result = await this.client.runMethod(address, 'get_user_access', [
                { type: 'slice', cell: beginCell().storeAddress(userAddr).endCell() }
            ]);
            
            const hasAccess = result.stack.readNumber();
            return hasAccess === 1;
            
        } catch (error) {
            console.error('Ошибка проверки доступа:', error.message);
            return false;
        }
    }
    
    async getUserBalance(userAddress) {
        try {
            const address = Address.parse(this.contractAddress);
            const userAddr = Address.parse(userAddress);
            
            const result = await this.client.runMethod(address, 'get_user_balance_method', [
                { type: 'slice', cell: beginCell().storeAddress(userAddr).endCell() }
            ]);
            
            return result.stack.readBigNumber().toString();
            
        } catch (error) {
            console.error('Ошибка получения баланса пользователя:', error.message);
            return '0';
        }
    }
    
    async getServiceBalance() {
        try {
            const address = Address.parse(this.contractAddress);
            
            const result = await this.client.runMethod(address, 'get_service_balance', []);
            
            return result.stack.readBigNumber().toString();
            
        } catch (error) {
            console.error('Ошибка получения баланса сервиса:', error.message);
            return '0';
        }
    }
    
    async withdrawUser(userAddress) {
        try {
            // Эта функция должна быть вызвана самим пользователем
            // Здесь мы только возвращаем инструкции
            return {
                success: true,
                message: 'Для вывода средств отправьте транзакцию с op=2 на адрес контракта',
                contractAddress: this.contractAddress,
                operation: 2
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    async withdrawService() {
        try {
            if (!this.keyPair) {
                throw new Error('Кошелек владельца не настроен');
            }
            
            // Здесь должна быть логика отправки транзакции вывода средств сервиса
            return {
                success: true,
                message: 'Функция вывода средств сервиса в разработке'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = TonClientWrapper;
