/**
 * Copyright (c) Whales Corp.
 * All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Address } from "../address/Address";
import { ExternalAddress } from "../address/ExternalAddress";
export declare function testAddress(workchain: number, seed: string): Address;
export declare function testExternalAddress(seed: string): ExternalAddress;
