const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Компиляция смарт-контракта FunC в BOC
 */
async function compileContract() {
    console.log('🔨 Компиляция смарт-контракта...');
    
    const contractPath = path.join(__dirname, '../contracts/access_control.fc');
    const outputPath = path.join(__dirname, '../build');
    
    // Создаем папку build если её нет
    if (!fs.existsSync(outputPath)) {
        fs.mkdirSync(outputPath, { recursive: true });
    }
    
    try {
        // Проверяем наличие func компилятора
        try {
            execSync('func -V', { stdio: 'pipe' });
        } catch (error) {
            console.error('❌ FunC компилятор не найден!');
            console.log('📥 Установите TON Development Environment:');
            console.log('   https://docs.ton.org/develop/smart-contracts/environment/installation');
            process.exit(1);
        }
        
        // Компилируем контракт
        const compileCommand = `func -o ${outputPath}/access_control.fif -SPA ${contractPath}`;
        console.log(`Выполняем: ${compileCommand}`);
        
        execSync(compileCommand, { stdio: 'inherit' });
        
        // Создаем BOC файл
        const createBocCommand = `fift -s ${path.join(__dirname, 'create-boc.fif')}`;
        execSync(createBocCommand, { stdio: 'inherit' });
        
        console.log('✅ Контракт успешно скомпилирован!');
        console.log(`📁 Файлы сохранены в: ${outputPath}`);
        
        return {
            success: true,
            outputPath: outputPath,
            contractFile: path.join(outputPath, 'access_control.boc')
        };
        
    } catch (error) {
        console.error('❌ Ошибка компиляции:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

// Создаем Fift скрипт для генерации BOC
function createFiftScript() {
    const fiftScript = `#!/usr/bin/fift -s
"TonUtil.fif" include

"../build/access_control.fif" include

// Создаем начальные данные контракта
<b 
    // owner_address (нужно заменить на реальный адрес)
    0 8 u, 
    // user_balances (пустой словарь)
    dictnew 
    // service_balance (0)
    0 Gram, 
    // access_records (пустой словарь)  
    dictnew
b> constant init-data

<b init-data ref, code ref, b> constant state-init

state-init 2 boc+>B "../build/access_control.boc" B>file
."Contract BOC saved to access_control.boc" cr
`;
    
    const scriptPath = path.join(__dirname, 'create-boc.fif');
    fs.writeFileSync(scriptPath, fiftScript);
    console.log('📝 Fift скрипт создан');
}

// Запускаем компиляцию если скрипт вызван напрямую
if (require.main === module) {
    createFiftScript();
    compileContract().then(result => {
        if (result.success) {
            console.log('🎉 Компиляция завершена успешно!');
        } else {
            console.error('💥 Компиляция не удалась');
            process.exit(1);
        }
    });
}

module.exports = { compileContract };
